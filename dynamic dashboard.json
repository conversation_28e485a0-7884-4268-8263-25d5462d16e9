[{"id": "c0f1d9d0d2a1b111", "type": "tab", "label": "Dynamic Dashboards", "disabled": false, "info": ""}, {"id": "6b8d0284716b7163", "type": "group", "z": "c0f1d9d0d2a1b111", "name": "Trigger start daily", "style": {"label": true}, "nodes": ["0fb25f5680c98c2c", "335f2e81dcc121fc", "a0fa59ed94d4501f"], "x": 174, "y": 79, "w": 292, "h": 82}, {"id": "b0586d501c595f39", "type": "group", "z": "c0f1d9d0d2a1b111", "name": "Fetch Edges page-by-page", "style": {"label": true}, "nodes": ["a77cd30e3d1fa5f0", "bde4332458771fa7", "a34d3267d829d12c", "c49b13db790d6718", "e1f2a3b4c5d6e7f8", "93a4b5c6d7e8f999", "6a7b8c9<PERSON><PERSON><PERSON><PERSON>", "b2d3e4f50617a588"], "x": 174, "y": 239, "w": 292, "h": 162}, {"id": "19f85f9c9df0a0c4", "type": "group", "z": "c0f1d9d0d2a1b111", "name": "Finalize Output", "style": {"label": true}, "nodes": ["1338477af0755897", "890890476911ab33", "0c54dc115fbbed2d", "f0e5d279fcc2d0a0", "73c5ced6e18209f4", "c23652b659152f32"], "x": 514, "y": 239, "w": 632, "h": 122}, {"id": "f2e658ec88f6901e", "type": "group", "z": "c0f1d9d0d2a1b111", "name": "Get MWEdge Source Status", "style": {"label": true}, "nodes": ["38b688da402a40d8", "39031cb793ad41f8", "9b9d99ed137a4613", "e3e74e19c17a2b17", "d395e5e9628eec68", "9690ad23a6b3666d", "aadc5434eb6b9fcb"], "x": 494, "y": 819, "w": 472, "h": 142}, {"id": "e127cde64e91ee69", "type": "group", "z": "c0f1d9d0d2a1b111", "name": "Render HTML", "style": {"label": true}, "nodes": ["1d0e1a0e6f9c1b52", "8a7d2e3dce28b0d1", "c63a564ba1d765b9"], "x": 494, "y": 979, "w": 492, "h": 122}, {"id": "6174a718383cb394", "type": "group", "z": "c0f1d9d0d2a1b111", "name": "Set Variables based on Edge and Stream Name", "style": {"label": true}, "nodes": ["661f164ced477111", "7c5b0e8dbc69be65", "7f79c16b1c124af2", "7ba5c861156b7b8e"], "x": 494, "y": 679, "w": 802, "h": 82}, {"id": "1338477af0755897", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "19f85f9c9df0a0c4", "name": "Generate Edge list", "func": "// Read from msg.payload.rows and write result to msg.edges\nif (!msg.payload || !Array.isArray(msg.payload.rows)) {\n    node.error(\"msg.payload.rows is not an array\");\n    return null;\n}\n\n// keep only the requested fields\nfunction pick4(x) {\n    if (!x || typeof x !== 'object' || Array.isArray(x)) return null;\n    return {\n        id: x.id ?? null,\n        name: x.name ?? null,\n        stream: x.stream ?? null,\n        protocol: x.protocol ?? null\n    };\n}\n\n// normalize input that might be an array, object, or object-of-arrays -> flat array of items\nfunction normalizeList(maybe) {\n    if (!maybe) return null;\n\n    // already an array?\n    if (Array.isArray(maybe)) {\n        // flatten one level if it's array-of-arrays\n        if (maybe.length && Array.isArray(maybe[0])) {\n            return [].concat.apply([], maybe);\n        }\n        return maybe;\n    }\n\n    // if it's an object: get its values\n    if (typeof maybe === 'object') {\n        var vals = Object.values(maybe);\n        // if values are arrays, flatten one level\n        if (vals.length && Array.isArray(vals[0])) {\n            return [].concat.apply([], vals);\n        }\n        return vals;\n    }\n\n    return null;\n}\n\nvar result = msg.payload.rows.map(function (o) {\n    // pick a name (root name or licenseInstallStatus.name as fallback)\n    var name = null;\n    if (o && o.name != null) name = o.name;\n    else if (o && o.licenseInstallStatus && o.licenseInstallStatus.name != null) {\n        name = o.licenseInstallStatus.name;\n    }\n\n    // pick configured* (prefer root, then look under o.payload.*)\n    var configuredOutputsRaw = (o && o.configuredOutputs != null) ? o.configuredOutputs\n        : (o && o.payload && o.payload.configuredOutputs != null ? o.payload.configuredOutputs : null);\n    var configuredSourcesRaw = (o && o.configuredSources != null) ? o.configuredSources\n        : (o && o.payload && o.payload.configuredSources != null ? o.payload.configuredSources : null);\n    var configuredStreams  = (o && o.configuredStreams != null) ? o.configuredStreams\n        : (o && o.payload && o.payload.configuredStreams != null ? o.payload.configuredStreams : null);\n\n    // simplify to only { id, name, stream, protocol } — no grouping\n    var outputsArr = normalizeList(configuredOutputsRaw);\n    var simplifiedOutputs = outputsArr ? outputsArr.map(pick4).filter(Boolean) : null;\n\n    var sourcesArr = normalizeList(configuredSourcesRaw);\n    var simplifiedSources = sourcesArr ? sourcesArr.map(pick4).filter(Boolean) : null;\n\n    return {\n        _id: (o && o._id != null) ? o._id : (o && o.id != null ? o.id : null),\n        name: name,\n        configuredOutputs: simplifiedOutputs,   // array of { id, name, stream, protocol }\n        configuredSources: simplifiedSources,   // array of { id, name, stream, protocol }\n        configuredStreams: configuredStreams    // unchanged\n    };\n});\n\ndelete msg.payload; // keep if you want to drop the original payload\nmsg.edges = result;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 630, "y": 280, "wires": [["890890476911ab33", "0c54dc115fbbed2d"]]}, {"id": "i4h5g6f7e8d9cabb", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "a1b2c3d4e5f6g7h8", "name": "Handle UI Input", "func": "// Process HTTP POST requests from the UI\nconst requestData = msg.payload;\n\n// ONLY process explicit user switch requests with proper validation\nif (requestData && requestData.action === 'switch' && requestData.currentSource && requestData.switchType === 'preselected') {\n    // Additional validation: must have timestamp and be recent (within last 5 seconds)\n    if (!requestData.timestamp) {\n        node.log('[UI INPUT] Rejected: No timestamp in switch request');\n        return [null];\n    }\n\n    const msgTime = new Date(requestData.timestamp).getTime();\n    const now = Date.now();\n    const timeDiff = now - msgTime;\n\n    if (timeDiff > 5000) { // More than 5 seconds old\n        node.log(`[UI INPUT] Rejected: Switch request too old (${timeDiff}ms)`);\n        return [null];\n    }\n\n    // Enhanced duplicate prevention with unique request ID\n    const requestId = `${requestData.currentSource}_${msgTime}`;\n    const lastRequestId = flow.get('lastSwitchRequestId') || '';\n    \n    if (requestId === lastRequestId) {\n        node.log('[UI INPUT] Rejected: Duplicate switch request detected');\n        return [null];\n    }\n\n    // Check for rapid-fire requests (prevent multiple clicks)\n    const lastSwitchTime = flow.get('lastSwitchRequestTime') || 0;\n    if (now - lastSwitchTime < 2000) { // Increased to 2 seconds for better protection\n        node.log('[UI INPUT] Rejected: Switch request too soon after previous request');\n        return [null];\n    }\n\n    // Store the request details to prevent duplicates\n    flow.set('lastSwitchRequestTime', now);\n    flow.set('lastSwitchRequestId', requestId);\n\n    // Get edge configuration from request data\n    const variables = requestData.variables;\n    const edgeId = variables?.edgeId;\n    const sources = variables?.sources || [];\n    \n    if (!edgeId || !sources.length) {\n        node.error('No edge configuration found in request variables');\n        return [null];\n    }\n\n    // Find the target source by ID (dynamic dashboard uses IDs, not types)\n    const targetSource = sources.find(s => s.id === requestData.currentSource);\n    \n    if (!targetSource) {\n        node.error(`No source found for ID: ${requestData.currentSource}`);\n        return [null];\n    }\n\n    node.log(`[UI INPUT] Processing valid switch request to: ${targetSource.name || requestData.currentSource}`);\n\n    // Prepare message for switch handler\n    const switchMsg = {\n        payload: requestData.currentSource,\n        change: `User requested switch to ${targetSource.name || requestData.currentSource}`,\n        variables: variables, // Pass through the variables for edge config\n        requestId: requestId // Include request ID for tracking\n    };\n\n    node.log('[UI INPUT] Triggering switch API');\n\n    // Output to the switch API flow\n    return [switchMsg];\n}\n\n// For all other message types, reject them\nnode.log(`[UI INPUT] Rejected: Invalid message type - action=${requestData?.action}, payload=${JSON.stringify(requestData)}`);\nreturn [null];", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1055, "y": 1180, "wires": [["f1e2d3c4b5a69788"]], "l": false}, {"id": "890890476911ab33", "type": "join", "z": "c0f1d9d0d2a1b111", "g": "19f85f9c9df0a0c4", "name": "", "mode": "custom", "build": "array", "property": "edges", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": true, "accumulate": false, "timeout": "", "count": "", "reduceRight": false, "reduceExp": "", "reduceInit": "", "reduceInitType": "", "reduceFixup": "", "x": 790, "y": 280, "wires": [["f0e5d279fcc2d0a0", "73c5ced6e18209f4"]]}, {"id": "0c54dc115fbbed2d", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "19f85f9c9df0a0c4", "name": "Debug OK: TEXCORE Get MWEdges", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 755, "y": 320, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "f1e2d3c4b5a69788", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "a1b2c3d4e5f6g7h8", "name": "Format Switch API Request", "func": "// Get the new source from the previous node's output\nconst newSourceId = msg.payload;\nconst variables = msg.variables;\n\nif (!variables || !newSourceId) {\n    node.error('Missing variables or source ID');\n    return null;\n}\n\n// Log the request intent\nnode.log(`[REQ] API call to switch source to ID: ${newSourceId}`);\n\n// Find the source configuration by ID\nconst sources = variables.sources || [];\nconst targetSource = sources.find(s => s.id === newSourceId);\n\nif (!targetSource) {\n    node.error(`No source found for ID: ${newSourceId}`);\n    return null;\n}\n\n// Get API credentials from global context\nconst baseUrl = global.get('SECRETS.TECHEX_TXCORE_URL_PROD');\nconst apiKey = global.get('SECRETS.TECHEX_TXCORE_API_KEY_PROD');\nconst edgeId = variables.edgeId;\n\nif (!baseUrl || !apiKey || !edgeId) {\n    node.error('Missing API credentials or edge ID');\n    return null;\n}\n\n// Create the message for the API call to activate the selected source\nmsg.url = `${baseUrl}/api/mwedge/${edgeId}/source/${targetSource.id}`;\nmsg.method = 'PUT';\nmsg.headers = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'Authorization': `Bearer ${apiKey}`\n};\nmsg.payload = { active: true };\nmsg.currentSource = newSourceId;\nmsg.targetSourceName = targetSource.name;\n\nnode.log(`[REQ] Switching to source: ${targetSource.name} (${targetSource.id})`);\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1115, "y": 1240, "wires": [["g2f3e4d5c6b7a899"]], "l": false}, {"id": "g2f3e4d5c6b7a899", "type": "http request", "z": "c0f1d9d0d2a1b111", "g": "a1b2c3d4e5f6g7h8", "name": "Switch API Call", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1185, "y": 1240, "wires": [["h3g4f5e6d7c8b9aa"]], "l": false}, {"id": "h3g4f5e6d7c8b9aa", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "a1b2c3d4e5f6g7h8", "name": "Handle Switch Response", "func": "const statusCode = msg.statusCode || 0;\nconst time = new Date().toISOString();\nconst sourceName = msg.targetSourceName || msg.currentSource;\n\n// Error path: If the API call fails\nif (statusCode !== 200) {\n    const errorPayload = {\n        success: false,\n        action: `Switch to ${sourceName}`,\n        error: `API call failed with status ${statusCode}`,\n        details: `Response: ${JSON.stringify(msg.payload)}`,\n        timestamp: time\n    };\n    \n    msg.payload = errorPayload;\n    msg.topic = \"error\";\n    \n    node.error(`[ERROR] Switch API returned ${statusCode}`, msg);\n    return [null, msg]; // Send to error debug and UI\n}\n\n// Success path: If the API call is successful\nconst successPayload = {\n    success: true,\n    action: `Switched to ${sourceName}`,\n    message: `Source successfully switched to '${sourceName}'`,\n    timestamp: time\n};\n\nmsg.payload = successPayload;\nmsg.topic = \"success\";\n\nnode.log(`[SUCCESS] Source switched to '${sourceName}'`);\nreturn [msg, null]; // Send success to UI, error (null)", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1265, "y": 1240, "wires": [["1d0e1a0e6f9c1b52", "k6j7i8h9g0f1ebcd"], ["1d0e1a0e6f9c1b52", "k6j7i8h9g0f1ebcd"]], "l": false}, {"id": "j5i6h7g8f9e0dabc", "type": "http in", "z": "c0f1d9d0d2a1b111", "name": "Switch Request Endpoint", "url": "edgeSwitches/:edge/:stream/switch", "method": "post", "upload": false, "swaggerDoc": "", "x": 1055, "y": 1120, "wires": [["i4h5g6f7e8d9cabb"]]}, {"id": "k6j7i8h9g0f1ebcd", "type": "http response", "z": "c0f1d9d0d2a1b111", "name": "Switch Response", "statusCode": "", "headers": {}, "x": 1345, "y": 1180, "wires": []}, {"id": "0fb25f5680c98c2c", "type": "inject", "z": "c0f1d9d0d2a1b111", "g": "6b8d0284716b7163", "name": "Start", "props": [{"p": "payload"}], "repeat": "", "crontab": "01 00 * * *", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 270, "y": 120, "wires": [["a0fa59ed94d4501f"]]}, {"id": "335f2e81dcc121fc", "type": "change", "z": "c0f1d9d0d2a1b111", "g": "6b8d0284716b7163", "name": "Set for iteration", "rules": [{"t": "set", "p": "search", "pt": "msg", "to": "", "tot": "str"}, {"t": "set", "p": "limit", "pt": "msg", "to": "200", "tot": "num"}, {"t": "set", "p": "include", "pt": "msg", "to": "_id,name,configuredSources,configuredStreams,configuredOutputs", "tot": "str"}, {"t": "set", "p": "skip", "pt": "msg", "to": "0", "tot": "num"}, {"t": "set", "p": "parts", "pt": "msg", "to": "{}", "tot": "json"}, {"t": "set", "p": "parts.id", "pt": "msg", "to": "_msgid", "tot": "msg"}, {"t": "set", "p": "parts.index", "pt": "msg", "to": "0", "tot": "num"}, {"t": "set", "p": "resetPagination", "pt": "msg", "to": "true", "tot": "bool"}], "x": 425, "y": 120, "wires": [["a77cd30e3d1fa5f0"]], "l": false}, {"id": "a77cd30e3d1fa5f0", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "Prepare request", "func": "// 1) Extract configuration (from msg.techex)\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token   = msg.techex?.apiKeyProd;\n\n// 2) Validate required fields\nif (!baseUrl || !token) {\n  return [null, {\n    error: \"Missing configuration: baseUrlProd or apiKeyProd\",\n    statusCode: 400,\n    details: { hasBaseUrl: !!baseUrl, hasToken: !!token }\n  }];\n}\n\n// 3) Optional: rate limiting\nconst operationName = 'GetMWEdges';\nconst rlCfg = { enabled: msg.rateLimit?.enabled !== false, intervalMs: Number(msg.rateLimit?.intervalMs) || 50 };\nif (rlCfg.enabled) {\n  const rlKey = `rateLimit.${operationName}`;\n  const last  = flow.get(rlKey) || 0;\n  if (Date.now() - last < rlCfg.intervalMs) {\n    const retryAfter = rlCfg.intervalMs - (Date.now() - last);\n    return [null, { error: \"Rate limit: Too many requests\", statusCode: 429, details: { operationName, intervalMs: rlCfg.intervalMs }, retryAfter }];\n  }\n  flow.set(rlKey, Date.now());\n}\n\ntry {\n  // 4) Build request\n  const endpoint = '/api/mwedges';\n  msg.url = baseUrl.replace(/\\/$/, '') + endpoint;\n  msg.method = 'GET';\n\n  // 5) Fixed query params\n  const params = {\n    limit: msg.limit,\n    skip: msg.skip,\n    name: msg.search,\n    includeFields: msg.include\n  };\n  msg.params = params;\n\n  // Serialize params\n  const query = Object.entries(params)\n    .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(String(v))}`)\n    .join('&');\n  if (query) msg.url += `?${query}`;\n\n  // 6) Headers\n  msg.headers = {\n    Authorization: `Bearer ${token}`,\n    Accept: 'application/json',\n    'Content-Type': 'application/json'\n  };\n\n  node.status({ fill: 'green', shape: 'dot', text: `${msg.method} ${msg.url}` });\n  msg.statusCode = 200;\n  return [msg, null];\n} catch (err) {\n  node.error('API setup failed', err);\n  return [null, { error: err.message, statusCode: 500, details: { stack: err.stack } }];\n}", "outputs": 2, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 215, "y": 280, "wires": [["bde4332458771fa7"], []], "l": false}, {"id": "a0fa59ed94d4501f", "type": "change", "z": "c0f1d9d0d2a1b111", "g": "6b8d0284716b7163", "name": "Get TXCORE secrets", "rules": [{"t": "set", "p": "techex.baseUrlProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_URL_PROD", "tot": "global"}, {"t": "set", "p": "techex.apiKeyProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_API_KEY_PROD", "tot": "global"}, {"t": "set", "p": "method", "pt": "msg", "to": "GET", "tot": "str"}, {"t": "set", "p": "payload", "pt": "msg", "to": "", "tot": "str"}, {"t": "set", "p": "msg.qibb.timeout_in_sec", "pt": "msg", "to": "1800", "tot": "num"}], "x": 365, "y": 120, "wires": [["335f2e81dcc121fc"]], "l": false}, {"id": "bde4332458771fa7", "type": "http request", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 285, "y": 280, "wires": [["c49b13db790d6718"]], "l": false}, {"id": "a34d3267d829d12c", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "Debug OK: TEXCORE Get MWEdges", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 405, "y": 280, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "c49b13db790d6718", "type": "change", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "", "rules": [{"t": "set", "p": "parts.count", "pt": "msg", "to": "$ceil(payload.count / limit)", "tot": "jsonata"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 345, "y": 280, "wires": [["a34d3267d829d12c", "1338477af0755897", "b2d3e4f50617a588"]], "l": false}, {"id": "f0e5d279fcc2d0a0", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "19f85f9c9df0a0c4", "name": "<PERSON><PERSON> lists", "func": "// Expect msg.edges to be an array of arrays\nif (!Array.isArray(msg.edges)) {\n    node.error(\"Expected msg.edges to be an array (of arrays).\");\n    return null;\n}\n\n// Flatten one level (array-of-arrays)\nvar flattened = msg.edges.reduce(function (acc, cur) {\n    if (Array.isArray(cur)) {\n        for (var i = 0; i < cur.length; i++) acc.push(cur[i]);\n    } else {\n        // In case there's a non-array item, include it as-is\n        acc.push(cur);\n    }\n    return acc;\n}, []);\n\n// Helper: find \"<token1>-<token2>\" (the part before the second \"-\") inside a string/object\nfunction findTwoTokenPrefix(item) {\n    // Matches any occurrence of two hyphen-separated chunks followed by a second hyphen,\n    // e.g. \"FOO-12-...\" -> captures \"FOO-12\"\n    var re = /([^- \\t\\r\\n]+-[^- \\t\\r\\n]+)-/;\n\n    function scanString(s) {\n        var m = s.match(re);\n        return m ? m[1] : null;\n    }\n\n    if (typeof item === \"string\") {\n        return scanString(item);\n    }\n    if (item && typeof item === \"object\") {\n        // check common fields first\n        var fields = [\"id\", \"name\", \"label\", \"key\", \"edge\", \"ref\", \"title\", \"text\"];\n        for (var i = 0; i < fields.length; i++) {\n            var v = item[fields[i]];\n            if (typeof v === \"string\") {\n                var r1 = scanString(v);\n                if (r1) return r1;\n            }\n        }\n        // fallback: scan all string props and simple arrays\n        for (var k in item) {\n            if (!Object.prototype.hasOwnProperty.call(item, k)) continue;\n            var val = item[k];\n            if (typeof val === \"string\") {\n                var r2 = scanString(val);\n                if (r2) return r2;\n            } else if (Array.isArray(val)) {\n                for (var j = 0; j < val.length; j++) {\n                    if (typeof val[j] === \"string\") {\n                        var r3 = scanString(val[j]);\n                        if (r3) return r3;\n                    }\n                }\n            }\n        }\n    }\n    return null;\n}\n\n// Group by \"<token1>-<token2>\" -> { \"FOO-12\": [...], \"BAR-7\": [...], ... }\nvar groups = {};\nfor (var i = 0; i < flattened.length; i++) {\n    var item = flattened[i];\n    var key = findTwoTokenPrefix(item);\n    if (key != null) {\n        if (!groups[key]) groups[key] = [];\n        groups[key].push(item);\n    } else {\n        // keep non-matching items if you want them accessible\n        if (!groups.unmatched) groups.unmatched = [];\n        groups.unmatched.push(item);\n    }\n}\n\n// Attach generation timestamp metadata\nvar now = new Date();\ngroups._meta = {\n    timestamp: now.toISOString(),\n    epoch_ms: now.getTime()\n};\n\n// a) put grouped object on msg.edges as { \"<token1>-<token2>\": [...], ..., _meta:{...}}\nmsg.edges = groups;\n\n// b) also on msg.global.edges (message-scoped object mirror)\nif (!msg.global || typeof msg.global !== \"object\") {\n    msg.global = {};\n}\nmsg.global.edges = JSON.parse(JSON.stringify(groups)); // decouple references\n\n// c) store in global context so other flows/nodes can read: global.get('edges')[\"FOO-12\"]\nglobal.set(\"edges\", groups);\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 960, "y": 280, "wires": [["c23652b659152f32"]]}, {"id": "73c5ced6e18209f4", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "19f85f9c9df0a0c4", "name": "Debug OK: TEXCORE Get MWEdges", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 895, "y": 320, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "f3b2a7a9e9c24111", "type": "comment", "z": "c0f1d9d0d2a1b111", "name": "Dynamically fetch all Widget Information", "info": "This fetches widget info either from \nGlobal context data or directly from TX Core", "x": 340, "y": 620, "wires": []}, {"id": "e1f2a3b4c5d6e7f8", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "Done", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 405, "y": 360, "wires": [], "l": false}, {"id": "93a4b5c6d7e8f999", "type": "change", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "Set parts index and skip", "rules": [{"t": "set", "p": "parts.index", "pt": "msg", "to": "$number(parts.index) + 1", "tot": "jsonata"}, {"t": "set", "p": "skip", "pt": "msg", "to": "$number(skip) + $number(limit)", "tot": "jsonata"}], "x": 285, "y": 360, "wires": [["6a7b8c9<PERSON><PERSON><PERSON><PERSON>"]], "l": false}, {"id": "6a7b8c9<PERSON><PERSON><PERSON><PERSON>", "type": "switch", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "More pages?", "property": "parts.index", "propertyType": "msg", "rules": [{"t": "lt", "v": "parts.count", "vt": "msg"}, {"t": "gte", "v": "parts.count", "vt": "msg"}], "checkall": "true", "repair": false, "outputs": 2, "x": 345, "y": 360, "wires": [["a77cd30e3d1fa5f0"], ["e1f2a3b4c5d6e7f8"]], "l": false}, {"id": "b2d3e4f50617a588", "type": "delay", "z": "c0f1d9d0d2a1b111", "g": "b0586d501c595f39", "name": "Delay between calls", "pauseType": "delay", "timeout": "5", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "180", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 215, "y": 360, "wires": [["93a4b5c6d7e8f999"]], "l": false}, {"id": "c23652b659152f32", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "19f85f9c9df0a0c4", "name": "Debug OK: TEXCORE Get MWEdges", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1085, "y": 280, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "a1b2c3d4e5f6g7h8", "type": "group", "z": "c0f1d9d0d2a1b111", "name": "[007] - Source Switching Logic", "style": {"label": true, "fill-opacity": "0.5"}, "nodes": ["f1e2d3c4b5a69788", "g2f3e4d5c6b7a899", "h3g4f5e6d7c8b9aa", "i4h5g6f7e8d9cabb", "j5i6h7g8f9e0dabc", "k6j7i8h9g0f1ebcd"], "x": 1014, "y": 1099, "w": 392, "h": 242}, {"id": "1d0e1a0e6f9c1b52", "type": "template", "z": "c0f1d9d0d2a1b111", "g": "e127cde64e91ee69", "name": "Render widget HTML", "field": "payload", "fieldType": "msg", "format": "handlebars", "syntax": "mustache", "template": "<!doctype html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" />\n  <title>{{variables.edgeName}}</title>\n  <style>\n    :root { --bg:#0f1214; --panel:#141a1f; --border:#22303a; --text:#cbd5e1; --muted:#8b9aa6; --accent:#60a5fa; --ok:#34d399; }\n    *{box-sizing:border-box} body{margin:0;background:var(--bg);font-family:ui-sans-serif,system-ui,-apple-system,\"Segoe UI\",Roboto,Inter,\"Helvetica Neue\",<PERSON><PERSON>,\"Noto Sans\",sans-serif;color:var(--text)}\n    .wrap{min-height:100vh;display:grid;place-items:center;padding:24px}\n    .card{width:min(720px,92vw);background:var(--panel);border:1px solid var(--border);border-radius:16px;padding:18px 20px 20px 20px;box-shadow:0 8px 24px rgba(0,0,0,.35)}\n    .header{display:flex;align-items:center;gap:10px;border-bottom:1px solid var(--border);padding-bottom:10px;margin-bottom:14px}\n    .lock{font-size:24px;opacity:.9;cursor: pointer;}\n    .title{font-weight:700;letter-spacing:.2px}\n    .status{margin-left:auto;font-size:12px;color:var(--ok)}\n    .row{display:flex;gap:14px;align-items:center;padding:9px 0;border-bottom:1px dashed var(--border)}\n    .row:last-child{border-bottom:0}\n    .label{width:180px;color:var(--muted);font-family:ui-monospace,SFMono-Regular,Menlo,Consolas,monospace}\n    .value{flex:1;font-family:ui-monospace,SFMono-Regular,Menlo,Consolas,monospace}\n    select{width:100%;background:#0c1116;border:1px solid var(--border);border-radius:8px;padding:10px 12px;color:var(--text)}\n    select:disabled{background:#2a2a2a;color:#666;border-color:#333;cursor:not-allowed}\n    .btn{background:#3b82f6;border:0;border-radius:8px;padding:8px 12px;color:white;font-weight:600;cursor:pointer;margin-left:auto;display:flex;align-items:center;justify-content:center;gap:5px}\n    .btn:hover:not(:disabled){background:#2d77c7}\n    .btn:disabled{background:#2a2a2a;color:#666;cursor:not-allowed}\n    .btn.loading{background:#f59e0b;cursor:not-allowed}\n    .spinner{width:12px;height:12px;border:2px solid #ffffff33;border-top:2px solid #ffffff;border-radius:50%;animation:spin 1s linear infinite}\n    @keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}\n    ul{list-style:none;padding-left:0;margin:0}\n    li{padding:3px 0}\n    .green{color:var(--ok)}\n  </style>\n</head>\n<body>\n  <div class=\"wrap\">\n    <div class=\"card\">\n      <div class=\"header\">\n        <div class=\"lock\" id=\"lockIcon\">🔒</div>\n        <div class=\"title\">{{variables.edgeName}}</div>\n        <div class=\"status\">Active <span id=\"now\"></span></div>\n      </div>\n\n      <div class=\"row\">\n        <div class=\"label\">Stream:</div>\n        <div class=\"value green\">{{#variables.streamName}}{{variables.streamName}}{{/variables.streamName}}{{^variables.streamName}}—{{/variables.streamName}}</div>\n      </div>\n\n      <div class=\"row\">\n        <div class=\"label\">Active Source:</div>\n        <!-- Always display the NAME; JS resolves it from variables.sources by matching variables.activeSource (ID) -->\n        <div class=\"value\" id=\"currentSource\">—</div>\n        <button class=\"btn\" id=\"switchBtn\" disabled><span id=\"switchBtnText\">Switch Now</span></button>\n      </div>\n\n      <div class=\"row\">\n        <div class=\"label\">Source Selection:</div>\n        <div class=\"value\">\n          <select name=\"source\" id=\"source\">\n            <!-- Works for a single object OR an array of objects -->\n            {{#variables.sources}}\n              <option value=\"{{id}}\">{{name}}{{^name}}{{id}}{{/name}}</option>\n            {{/variables.sources}}\n            {{^variables.sources}}\n              <option value=\"\">—</option>\n            {{/variables.sources}}\n          </select>\n        </div>\n      </div>\n\n      <div class=\"row\">\n        <div class=\"label\">Outputs:</div>\n        <div class=\"value\">\n          <ul>\n            <!-- Preferred: variables.outputs (object or array of objects) -->\n            {{#variables.outputs}}\n              <!-- Show name when present; if a raw string item appears, fall back to the string value -->\n              <li>{{name}}{{^name}}{{.}}{{/name}}</li>\n            {{/variables.outputs}}\n\n            <!-- Back-compat: variables.output (object or array of objects/strings) -->\n            {{^variables.outputs}}\n              {{#variables.output}}\n                <li>{{name}}{{^name}}{{.}}{{/name}}</li>\n              {{/variables.output}}\n              {{^variables.output}}<li>—</li>{{/variables.output}}\n            {{/variables.outputs}}\n          </ul>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <script>\n    (function(scope) {\n    // Get DOM elements\n    const lockIcon = document.getElementById('lockIcon');\n    const sourceSelect = document.getElementById('source');\n    const currentSource = document.getElementById('currentSource');\n    const switchBtn = document.getElementById('switchBtn');\n    const switchBtnText = document.getElementById('switchBtnText');\n    const nowSpan = document.getElementById('now');\n\n    // State variables\n    let locked = true;\n    let availableSources = [];\n    let selectedSourceId = null;\n    let currentActiveSourceId = null;\n    let isLoading = false;\n\n    // SVG icons for lock/unlock\n    const lockSVG = '🔒';\n    const unlockSVG = '🔓';\n\n    // Initialize timestamp\n    nowSpan.textContent = new Date().toLocaleTimeString();\n\n    function toggleLock() {\n        locked = !locked;\n        lockIcon.innerHTML = locked ? lockSVG : unlockSVG;\n        updateButtonStates();\n    }\n\n    function updateButtonStates() {\n        sourceSelect.disabled = locked || isLoading;\n        // Switch Now enabled only if unlocked, not loading, source selected, and different from current\n        switchBtn.disabled = locked || isLoading || !selectedSourceId || selectedSourceId === currentActiveSourceId;\n    }\n\n    function setLoadingState(loading) {\n        isLoading = loading;\n        if (loading) {\n            switchBtn.classList.add('loading');\n            switchBtn.disabled = true;\n            switchBtnText.innerHTML = '<div class=\"spinner\"></div> Switching...';\n        } else {\n            switchBtn.classList.remove('loading');\n            switchBtnText.textContent = 'Switch Now';\n            updateButtonStates();\n        }\n    }\n\n    function updateActiveSource(activeSourceId) {\n        currentActiveSourceId = activeSourceId;\n        const activeSource = availableSources.find(s => s.id === activeSourceId);\n        if (activeSource) {\n            currentSource.textContent = activeSource.name || activeSource.id || '—';\n        } else {\n            currentSource.textContent = '—';\n        }\n        \n        // Pre-select in dropdown if not already selected\n        if (!selectedSourceId && activeSourceId) {\n            selectedSourceId = activeSourceId;\n            sourceSelect.value = activeSourceId;\n        }\n        \n        updateButtonStates();\n    }\n\n    function switchToSelectedSource() {\n        if (locked || isLoading || !selectedSourceId) return;\n        if (selectedSourceId === currentActiveSourceId) return;\n\n        setLoadingState(true);\n\n        // Send switch request via HTTP POST\n        const switchData = {\n            action: 'switch',\n            currentSource: selectedSourceId,\n            switchType: 'preselected',\n            timestamp: new Date().toISOString(),\n            variables: {\n                edgeId: \"{{variables.edgeId}}\",\n                edgeName: \"{{variables.edgeName}}\",\n                sources: availableSources\n            }\n        };\n\n        fetch(window.location.pathname + '/switch', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(switchData)\n        })\n        .then(response => response.json())\n        .then(data => {\n            setLoadingState(false);\n            if (data.success) {\n                nowSpan.textContent = new Date().toLocaleTimeString();\n                // Optionally refresh the page to get updated source status\n                setTimeout(() => window.location.reload(), 1000);\n            } else {\n                console.error('Switch failed:', data.error);\n            }\n        })\n        .catch(error => {\n            setLoadingState(false);\n            console.error('Switch request failed:', error);\n        });\n    }\n\n    // Event listeners\n    lockIcon.addEventListener('click', toggleLock);\n    switchBtn.addEventListener('click', switchToSelectedSource);\n    \n    sourceSelect.addEventListener('change', (e) => {\n        if (!locked && !isLoading && e.target.value) {\n            selectedSourceId = e.target.value;\n            updateButtonStates();\n        }\n    });\n\n    // Initialize with current data\n    const activeId = \"{{variables.activeSource}}\";\n    if (activeId) {\n        updateActiveSource(activeId);\n    }\n\n    // Store sources for reference\n    const sourcesData = {{#variables.sources}}[{{#.}}{\"id\":\"{{id}}\",\"name\":\"{{name}}\"}{{#unless @last}},{{/unless}}{{/.}}]{{/variables.sources}}{{^variables.sources}}[]{{/variables.sources}};\n    availableSources = sourcesData;\n\n    })(window);\n  </script>\n</body>\n</html>", "output": "str", "x": 620, "y": 1020, "wires": [["8a7d2e3dce28b0d1", "c63a564ba1d765b9"]]}, {"id": "8a7d2e3dce28b0d1", "type": "http response", "z": "c0f1d9d0d2a1b111", "g": "e127cde64e91ee69", "name": "Send HTML", "statusCode": "", "headers": {}, "x": 850, "y": 1020, "wires": []}, {"id": "38b688da402a40d8", "type": "http request", "z": "c0f1d9d0d2a1b111", "g": "f2e658ec88f6901e", "name": "", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 655, "y": 860, "wires": [["d395e5e9628eec68"]], "l": false}, {"id": "39031cb793ad41f8", "type": "change", "z": "c0f1d9d0d2a1b111", "g": "f2e658ec88f6901e", "name": "Get TXCORE secrets", "rules": [{"t": "set", "p": "techex.baseUrlProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_URL_PROD", "tot": "global"}, {"t": "set", "p": "techex.apiKeyProd", "pt": "msg", "to": "SECRETS.TECHEX_TXCORE_API_KEY_PROD", "tot": "global"}, {"t": "set", "p": "method", "pt": "msg", "to": "GET", "tot": "str"}, {"t": "set", "p": "techex.edgeId", "pt": "msg", "to": "variables.edgeId", "tot": "msg"}, {"t": "set", "p": "params.include<PERSON>ields", "pt": "msg", "to": "configuredSources", "tot": "str"}, {"t": "set", "p": "params.name", "pt": "msg", "to": "variables.edgeName", "tot": "msg"}], "x": 535, "y": 860, "wires": [["e3e74e19c17a2b17"]], "l": false}, {"id": "9b9d99ed137a4613", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "f2e658ec88f6901e", "name": "Debug Error: TEXCORE Get MWEdge", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 655, "y": 920, "wires": [], "icon": "node-red/alert.svg", "l": false, "info": "# Flow Debug Error Get Secret\n\n## Description\nThis `Debug` node is used to log and inspect errors that occur during the secret retrieval process. It captures the full message object (`msg`) and displays the `msg.statusCode` in the node's status field for quick monitoring.\n\n## Input\n- **Trigger:**\n  - The node is triggered by the output of a preceding node in the flow, typically when an error occurs during secret retrieval.\n- **Context / Global Vars:** None required.\n- **Expected Secrets:** None required.\n\n## Output\n- **Success Path:**\n  - Logs the full incoming message (`msg`) to the debug sidebar for inspection.\n  - Displays the `msg.statusCode` in the node's status field for quick reference.\n  - Outputs the message for further processing if connected downstream.\n- **Error Path:**\n  - This node does not handle errors but is used to inspect and debug issues in the incoming message.\n\n## Notes\n> - The debug node is enabled by default to assist in troubleshooting.\n> - Use this node during development or troubleshooting to identify and resolve issues in the secret retrieval process.\n> - The `msg.statusCode` is displayed in the node's status field for quick monitoring of the HTTP response status.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n"}, {"id": "e3e74e19c17a2b17", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "f2e658ec88f6901e", "name": "Get MWEdge", "func": "// 1. Extract configuration\nconst baseUrl = msg.techex?.baseUrlProd;\nconst token = msg.techex?.apiKeyProd;\n\n// 2. Validate required fields\nif (!baseUrl || !token) {\n    return [null, {\n        error: \"Missing configuration: baseUrlProd or apiKeyProd\",\n        statusCode: 400,\n        details: { hasBaseUrl: !!baseUrl, hasToken: !!token }\n    }];\n}\n\n// 3. Validate path parameter\nconst edgeId = msg.techex?.edgeId || msg.edgeId;\nif (!edgeId) {\n    return [null, {\n        error: \"Missing path parameter: edgeId\",\n        statusCode: 400,\n        details: {}\n    }];\n}\n\n// 4. Rate limiting\nconst operationName = 'GetMWEdge';\nconst rlKey = `rateLimit.${operationName}`;\nconst last = flow.get(rlKey) || 0;\nconst MIN_INTERVAL = 5;\nif (Date.now() - last < MIN_INTERVAL) {\n    const retryAfter = MIN_INTERVAL - (Date.now() - last);\n    return [null, {\n        error: \"Rate limit: Too many requests\",\n        statusCode: 429,\n        details: {},\n        retryAfter\n    }];\n}\nflow.set(rl<PERSON><PERSON>, Date.now());\n\n// 5. Build request\ntry {\n    // Construct URL with path parameter\n    msg.url = baseUrl.replace(/\\/$/, '') + '/api/mwedges';\n\n    msg.method = msg.method || 'GET';\n\n    // Attach query parameters if provided\n    if (msg.params && typeof msg.params === 'object') {\n        const query = Object.entries(msg.params)\n            .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)\n            .join('&');\n        if (query) {\n            msg.url += '?' + query;\n        }\n    }\n\n    // Set headers\n    msg.headers = {\n        ...(msg.headers || {}),\n        Authorization: `Bearer ${token}`,\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n    };\n\n    // Node status for debug\n    node.status({\n        fill: 'green',\n        shape: 'dot',\n        text: `${msg.method} ${msg.url}`\n    });\n\n    msg.statusCode = 200;\n\n    return [msg, null];\n} catch (err) {\n    node.error('API setup failed', err);\n    return [null, {\n        error: err.message,\n        statusCode: 500,\n        details: { stack: err.stack }\n    }];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 595, "y": 860, "wires": [["38b688da402a40d8"], ["9b9d99ed137a4613", "aadc5434eb6b9fcb"]], "l": false, "info": "# Get MWEdge\n\n## Purpose\nRetrieve details for a specific MWEdge instance by its unique identifier.\n\n## API Details\n- **Endpoint**: `GET /api/mwedge/:edgeId`\n- **Authentication**: Bearer <PERSON>ken\n- **Content-Type**: application/json\n\n## Input Configuration\n### Required\n- `msg.techex.baseUrlProd` – Base URL for API\n- `msg.techex.apiKeyProd` – JWT/API Token\n- `msg.techex.edgeId` or `msg.edgeId` – MWEdge unique identifier\n\n### Optional\n- `msg.method` – HTTP method (default: `GET`)\n- `msg.params` – Query parameters object (e.g., filters)\n\n## Outputs\n### Output 1 (Success)\nComplete `msg` object with:\n- `url` – Fully constructed endpoint URL\n- `method` – HTTP method\n- `headers` – Including Authorization\n- `statusCode` – 200 on success\n\n### Output 2 (Error)\n```json\n{\n    \"error\": \"Error description\",\n    \"statusCode\": <code>,\n    \"details\": {}\n}\n```\n\n## Error Scenarios\n- Missing configuration (400)\n- Missing required parameters (400)\n- Rate limiting (429)\n- Internal errors (500)\n\n## Implementation Notes\n1. Validates configuration before proceeding.\n2. Validates `edgeId` path parameter.\n3. Implements rate limiting (1 request per second).\n4. Constructs full URL and appends query parameters.\n5. Adds required headers for authentication.\n\n## Notes\n> Ensure upstream Change node provides the secrets and `edgeId` parameter.\n\n## Author\n[<NAME_EMAIL>](mailto:<EMAIL>)\n\n## qibb Docs\n[📚 qibb Platform Documentation](https://docs.qibb.com/platform/latest/)\n[📚 TechEx API Documentation](https://mwcore.techex.co.uk/docs/#api-MWEdge-GetMWEdge)"}, {"id": "d395e5e9628eec68", "type": "change", "z": "c0f1d9d0d2a1b111", "g": "f2e658ec88f6901e", "name": "", "rules": [{"t": "set", "p": "variables.activeSource", "pt": "msg", "to": "$string(\t  (payload.rows[].configuredSources[active = true and stream = variables.stream].id)[0]\t)", "tot": "jsonata"}, {"t": "delete", "p": "payload", "pt": "msg"}, {"t": "delete", "p": "techex", "pt": "msg"}, {"t": "delete", "p": "params", "pt": "msg"}, {"t": "delete", "p": "url", "pt": "msg"}, {"t": "delete", "p": "responseUrl", "pt": "msg"}, {"t": "set", "p": "headers", "pt": "msg", "to": "{\"Content-Type\":\"text/html; charset=utf-8\"}", "tot": "json"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 770, "y": 860, "wires": [["9690ad23a6b3666d", "1d0e1a0e6f9c1b52"]]}, {"id": "9690ad23a6b3666d", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "f2e658ec88f6901e", "name": "Debug OK: TEXCORE Get MWEdge", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 905, "y": 860, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "661f164ced477111", "type": "function", "z": "c0f1d9d0d2a1b111", "g": "6174a718383cb394", "name": "Dynamically fetch edge config", "func": "// 1) Build msg.variables.matchChannel by extracting \"<token1>-<token2>\" from the SECOND path segment\nconst url = (msg.req && msg.req.url) || msg.url || \"\";\nconst pathOnly = (url || \"\").split(/[?#]/)[0]; // strip query/hash\n\n// Example pathOnly:\n// \"/edgeSwitches/MATCH-1-CLEANFEED-eu-cont-az1/MATCH-1-CLEANFEED-SPL-eu-cont-az1\"\n// segments -> [\"edgeSwitches\",\"MATCH-1-CLEANFEED-eu-cont-az1\",\"MATCH-1-CLEANFEED-SPL-eu-cont-az1\"]\nconst segments = pathOnly.split(\"/\").filter(Boolean);\nconst secondSeg = segments[1] || \"\";\n\nlet key;\nif (secondSeg) {\n  let decoded = secondSeg;\n  try { decoded = decodeURIComponent(secondSeg); } catch (e) { /* ignore bad encodings */ }\n  const parts = decoded.split(\"-\").filter(Boolean);\n  if (parts.length >= 2) key = `${parts[0]}-${parts[1]}`; // take exactly the first two tokens\n}\n\nif (key) {\n  msg.variables = msg.variables || {};\n  msg.variables.matchChannel = key;\n} else {\n  node.warn(`Could not extract \"<token1>-<token2>\" from the second path segment: ${url}`);\n}\n\n// 2) Fetch from global.edges using the derived key, with helpful fallbacks\nconst edges = global.get('edges') || {};\nconst rawKey = msg?.variables?.matchChannel;\n\nif (typeof rawKey !== 'string' || !rawKey) {\n  msg.edges = undefined;\n  return msg;\n}\n\nfunction getValue(obj, k) {\n  if (obj == null) return undefined;\n\n  // Direct lookup (works for keys with \"-\" via bracket syntax)\n  if (Object.prototype.hasOwnProperty.call(obj, k)) return obj[k];\n\n  // Case-insensitive key match\n  const lower = k.toLowerCase();\n  for (const prop in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, prop) && prop.toLowerCase() === lower) {\n      return obj[prop];\n    }\n  }\n\n  // Legacy fallback: if key is \"MATCH-42\", try \"match42\"\n  const legacy = k.match(/^MATCH-(\\d+)$/i);\n  if (legacy) {\n    const legacyKey = `match${legacy[1]}`;\n    if (Object.prototype.hasOwnProperty.call(obj, legacyKey)) return obj[legacyKey];\n    // case-insensitive legacy\n    const legacyLower = legacyKey.toLowerCase();\n    for (const prop in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, prop) && prop.toLowerCase() === legacyLower) {\n        return obj[prop];\n      }\n    }\n  }\n\n  // Dotted/bracket path fallback: \"a.b[0].c\"\n  const path = k.replace(/\\[(\\w+)\\]/g, '.$1').replace(/^\\./, '');\n  return path.split('.').reduce((acc, seg) => (acc == null ? undefined : acc[seg]), obj);\n}\n\nconst value = getValue(edges, rawKey);\n\n// If you prefer a deep copy for safety, replace the next line with:\n// msg.edges = JSON.parse(JSON.stringify(value));\nmsg.edges = value;\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 720, "wires": [["7c5b0e8dbc69be65", "0748187ee2331b3a"]]}, {"id": "7c5b0e8dbc69be65", "type": "change", "z": "c0f1d9d0d2a1b111", "g": "6174a718383cb394", "name": "Set variables + headers", "rules": [{"t": "set", "p": "variables.edgeName", "pt": "msg", "to": "$split(req.url, \"/\")[2]", "tot": "jsonata"}, {"t": "set", "p": "variables.edgeBody", "pt": "msg", "to": "$single(edges[name = $$.variables.edgeName])", "tot": "jsonata"}, {"t": "set", "p": "variables.streamName", "pt": "msg", "to": "$split($substringBefore(req.url, \"?\"), \"/\")[3]", "tot": "jsonata"}, {"t": "set", "p": "variables.stream", "pt": "msg", "to": "($$.variables.edgeBody.configuredStreams[$string(name) = $string($$.variables.streamName)].id)[0]", "tot": "jsonata"}, {"t": "set", "p": "variables.sources", "pt": "msg", "to": "$$.variables.edgeBody.configuredSources[   $string(stream) = $string($$.variables.stream) ].{   \"name\": name,   \"id\": id }", "tot": "jsonata"}, {"t": "set", "p": "variables.outputs", "pt": "msg", "to": "$$.variables.edgeBody.configuredOutputs[   $string(stream) = $string($$.variables.stream) ].{   \"name\": name,   \"id\": id }", "tot": "jsonata"}, {"t": "set", "p": "variables.edgeId", "pt": "msg", "to": "variables.edgeBody._id", "tot": "msg"}, {"t": "set", "p": "rate", "pt": "msg", "to": "50", "tot": "num"}, {"t": "set", "p": "qibb.external_name", "pt": "msg", "to": "variables.edgeName", "tot": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 880, "y": 720, "wires": [["7ba5c861156b7b8e", "8858450c63847e16"]]}, {"id": "7f79c16b1c124af2", "type": "debug", "z": "c0f1d9d0d2a1b111", "g": "6174a718383cb394", "name": "Debug OK: TEXCORE Get MWEdge", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1235, "y": 720, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "7ba5c861156b7b8e", "type": "delay", "z": "c0f1d9d0d2a1b111", "g": "6174a718383cb394", "name": "", "pauseType": "rate", "timeout": "500", "timeoutUnits": "milliseconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": true, "outputs": 1, "x": 1070, "y": 720, "wires": [["7f79c16b1c124af2", "39031cb793ad41f8"]]}, {"id": "3c453f184631e084", "type": "http in", "z": "c0f1d9d0d2a1b111", "name": "", "url": "edgeSwitches/:edge/:stream", "method": "get", "upload": false, "swaggerDoc": "", "x": 190, "y": 720, "wires": [["1f0a0344d19484a5"]]}, {"id": "33e4ed3dc71430ca", "type": "function", "z": "c0f1d9d0d2a1b111", "name": "<PERSON><PERSON> lists", "func": "// Expect msg.edges to be an array of arrays\nif (!Array.isArray(msg.edges)) {\n    node.error(\"Expected msg.edges to be an array (of arrays).\");\n    return null;\n}\n\n// Flatten one level (array-of-arrays)\nvar flattened = msg.edges.reduce(function (acc, cur) {\n    if (Array.isArray(cur)) {\n        for (var i = 0; i < cur.length; i++) acc.push(cur[i]);\n    } else {\n        // In case there's a non-array item, include it as-is\n        acc.push(cur);\n    }\n    return acc;\n}, []);\n\n// Helper: find MATCH number (MATCH-N-...) inside a string/object\nfunction findMatchNumber(item) {\n    var re = /\\bMATCH-(\\d+)-/i;\n\n    if (typeof item === \"string\") {\n        var m = item.match(re);\n        return m ? parseInt(m[1], 10) : null;\n    }\n    if (item && typeof item === \"object\") {\n        // check common fields first\n        var fields = [\"id\", \"name\", \"label\", \"key\", \"edge\", \"ref\", \"title\", \"text\"];\n        for (var i = 0; i < fields.length; i++) {\n            var v = item[fields[i]];\n            if (typeof v === \"string\") {\n                var m1 = v.match(re);\n                if (m1) return parseInt(m1[1], 10);\n            }\n        }\n        // fallback: scan all string props and simple arrays\n        for (var k in item) {\n            if (!Object.prototype.hasOwnProperty.call(item, k)) continue;\n            var val = item[k];\n            if (typeof val === \"string\") {\n                var m2 = val.match(re);\n                if (m2) return parseInt(m2[1], 10);\n            } else if (Array.isArray(val)) {\n                for (var j = 0; j < val.length; j++) {\n                    if (typeof val[j] === \"string\") {\n                        var m3 = val[j].match(re);\n                        if (m3) return parseInt(m3[1], 10);\n                    }\n                }\n            }\n        }\n    }\n    return null;\n}\n\n// Group by match number -> { match1: [...], match2: [...], ... }\nvar groups = {};\nfor (var i = 0; i < flattened.length; i++) {\n    var item = flattened[i];\n    var n = findMatchNumber(item);\n    if (n != null) {\n        var key = \"match\" + n;\n        if (!groups[key]) groups[key] = [];\n        groups[key].push(item);\n    } else {\n        // keep non-matching items if you want them accessible\n        if (!groups.unmatched) groups.unmatched = [];\n        groups.unmatched.push(item);\n    }\n}\n\n// Attach generation timestamp metadata\nvar now = new Date();\ngroups._meta = {\n    timestamp: now.toISOString(),\n    epoch_ms: now.getTime()\n};\n\n// a) put grouped object on msg.edges as { match1: [...], match2: [...], ... , _meta:{...}}\nmsg.edges = groups;\n\n// b) also on msg.global.edges (message-scoped object mirror)\nif (!msg.global || typeof msg.global !== \"object\") {\n    msg.global = {};\n}\nmsg.global.edges = JSON.parse(JSON.stringify(groups)); // decouple references\n\n// c) store in global context so other flows/nodes can read: global.get('edges').match1\nglobal.set(\"edges\", groups);\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 960, "y": 540, "wires": [[]]}, {"id": "1f0a0344d19484a5", "type": "qibb-checkpoint", "z": "c0f1d9d0d2a1b111", "name": "Widget fetched", "CHECKPOINT_TYPE": "START", "RATE_LIMIT": "100", "MAX_AUTO_RETRY_ATTEMPTS": "0", "EVENT_DATA_STORAGE": "COMPACT", "LOG_SETTINGS": "MINIMUM", "POD_LEADERSHIP_CHECK": false, "x": 420, "y": 720, "wires": [["661f164ced477111"]]}, {"id": "c63a564ba1d765b9", "type": "qibb-checkpoint", "z": "c0f1d9d0d2a1b111", "g": "e127cde64e91ee69", "name": "Widget rendered", "CHECKPOINT_TYPE": "SUCCESS", "RATE_LIMIT": "100", "MAX_AUTO_RETRY_ATTEMPTS": "0", "EVENT_DATA_STORAGE": "COMPACT", "LOG_SETTINGS": "MINIMUM", "POD_LEADERSHIP_CHECK": false, "x": 870, "y": 1060, "wires": [[]]}, {"id": "aadc5434eb6b9fcb", "type": "qibb-checkpoint", "z": "c0f1d9d0d2a1b111", "g": "f2e658ec88f6901e", "name": "Widget fetch failed", "CHECKPOINT_TYPE": "FAIL", "RATE_LIMIT": "100", "MAX_AUTO_RETRY_ATTEMPTS": "0", "EVENT_DATA_STORAGE": "FULL", "LOG_SETTINGS": "ALL", "POD_LEADERSHIP_CHECK": false, "x": 810, "y": 920, "wires": [[]]}, {"id": "0748187ee2331b3a", "type": "debug", "z": "c0f1d9d0d2a1b111", "name": "Debug OK: TEXCORE Get MWEdge", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 845, "y": 640, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}, {"id": "8858450c63847e16", "type": "debug", "z": "c0f1d9d0d2a1b111", "name": "Debug OK: TEXCORE Get MWEdge", "active": true, "tosidebar": true, "console": false, "tostatus": true, "complete": "true", "targetType": "full", "statusVal": "statusCode", "statusType": "msg", "x": 1035, "y": 660, "wires": [], "icon": "font-awesome/fa-check-circle-o", "l": false}]